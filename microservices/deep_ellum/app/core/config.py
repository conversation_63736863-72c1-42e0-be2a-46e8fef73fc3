from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Application configuration settings."""

    # Service
    SERVICE_NAME: str = "Deep Ellum Service"

    # Database
    LOCAL_DATABASE_URI: str = "postgresql://postgres:password@localhost:5432/ellum_deep"
    PROD_DATABASE_URI: Optional[str] = None
    
    # Authentication
    AUTH_SERVICE_URL: str = "http://localhost:8000/api/v1/auth"
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI Services
    AI_PROVIDER: str = "gemini"  # "gemini" or "openai"
    GEMINI_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
    FIRECRAWL_API_KEY: Optional[str] = None
    LANGSMITH_API_KEY: Optional[str] = None
    LANGSMITH_PROJECT: str = "deep-ellum-agents"

    # Search Services
    GOOGLE_SEARCH_API_KEY: Optional[str] = None
    GOOGLE_SEARCH_ENGINE_ID: Optional[str] = None

    # FastBot Service Integration
    FASTBOT_SERVICE_URL: str = "http://localhost:8002"
    FASTBOT_KNOWLEDGEBASE_ENDPOINT: str = "/api/v1/file/knowledgebase"

    # Socials Service Integration
    SOCIALS_SERVICE_URL: str = "http://localhost:8005"

    # Qdrant Configuration (same as FastBot for knowledgebase access)
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: str = "6333"
    VECTOR_DIM: str = "768"  # Keep as string to match FastBot

    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # Service Configuration
    SERVICE_NAME: str = "deep-ellum-agent-service"
    SERVICE_PORT: int = 8003
    DEBUG: bool = False
    
    # Environment
    ENVIRONMENT: str = "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
