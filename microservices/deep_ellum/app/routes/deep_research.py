"""
Deep Research API routes using OpenAI's deep research models.
"""

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
from app.database.connection import get_db
from app.models.schemas import (
    DeepResearchRequest, 
    DeepResearchResponse, 
    DeepResearchStatus,
    DeepResearchModel
)
from app.services.openai_deep_research_service import openai_deep_research_service
from app.agents.openai_deep_research_agent import OpenAIDeepResearchAgent
from app.utils.auth import get_current_user, verify_organization, get_user_id
from app.utils.logger import get_logger
import asyncio

logger = get_logger(__name__)

router = APIRouter()


@router.post("/deep-research", response_model=DeepResearchResponse)
async def conduct_deep_research(
    request: DeepResearchRequest,
    background_tasks: BackgroundTasks,
    organization_id: str = Depends(verify_organization),
    user_id: str = Depends(get_user_id),
    db: AsyncSession = Depends(get_db),
    token: dict = Depends(get_current_user)
):
    """
    Conduct deep research using OpenAI's advanced research models.
    
    This endpoint uses OpenAI's o3-deep-research or o4-mini-deep-research models
    to conduct comprehensive research with web search and code interpretation.
    """
    try:
        logger.info(f"Starting deep research for organization {organization_id}")
        
        # Create research agent
        research_agent = OpenAIDeepResearchAgent()
        
        # Prepare context
        context = {
            "organization_id": organization_id,
            "user_id": user_id,
            "research_params": {
                "background": request.background,
                "max_tool_calls": request.max_tool_calls,
                "include_code_interpreter": request.include_code_interpreter,
                "include_web_search": request.include_web_search,
                "instructions": request.instructions
            }
        }
        
        # If background mode, start async task
        if request.background:
            # Start background research
            task_id = f"research_{organization_id}_{user_id}_{hash(request.query)}"
            background_tasks.add_task(
                _conduct_background_research,
                research_agent,
                request.query,
                context,
                task_id
            )
            
            return DeepResearchResponse(
                success=True,
                response_id=task_id,
                model=request.model.value,
                output_text="Research started in background mode. Use the response_id to check status.",
                metadata={"status": "started", "background": True}
            )
        else:
            # Conduct synchronous research
            result = await research_agent.process_message(request.query, context)
            
            return DeepResearchResponse(
                success=True,
                model=request.model.value,
                output_text=result,
                metadata={"status": "completed", "background": False}
            )
            
    except Exception as e:
        logger.error(f"Error in deep research endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to conduct deep research: {str(e)}"
        )


@router.get("/deep-research/status/{response_id}", response_model=DeepResearchStatus)
async def get_research_status(
    response_id: str,
    organization_id: str = Depends(verify_organization),
    user_id: str = Depends(get_user_id),
    token: dict = Depends(get_current_user)
):
    """Get the status of a deep research task."""
    try:
        # Get status from OpenAI service
        status_result = await openai_deep_research_service.get_research_status(response_id)
        
        return DeepResearchStatus(
            response_id=response_id,
            status=status_result.get("status", "unknown"),
            created_at=status_result.get("created_at"),
            completed_at=status_result.get("completed_at"),
            model=status_result.get("model"),
            error=status_result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error getting research status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get research status: {str(e)}"
        )


@router.get("/deep-research/models")
async def get_available_models(
    token: dict = Depends(get_current_user)
):
    """Get available deep research models and their capabilities."""
    return {
        "models": [
            {
                "name": DeepResearchModel.O3_DEEP_RESEARCH.value,
                "description": "Advanced deep research model for comprehensive analysis",
                "capabilities": [
                    "Multi-step research",
                    "Web search integration",
                    "Code interpretation",
                    "Hundreds of sources analysis",
                    "Research-analyst level reports"
                ],
                "use_cases": [
                    "Legal research",
                    "Scientific research", 
                    "Market analysis",
                    "Academic research",
                    "Policy analysis"
                ],
                "recommended_for": "Complex, comprehensive research tasks"
            },
            {
                "name": DeepResearchModel.O4_MINI_DEEP_RESEARCH.value,
                "description": "Efficient deep research model for faster results",
                "capabilities": [
                    "Streamlined research",
                    "Web search integration",
                    "Code interpretation",
                    "Focused analysis"
                ],
                "use_cases": [
                    "Quick market insights",
                    "Trend analysis",
                    "Competitive intelligence",
                    "Product research"
                ],
                "recommended_for": "Faster research with good depth"
            }
        ],
        "tools": [
            {
                "name": "web_search_preview",
                "description": "Search and browse web content",
                "actions": ["search", "open_page", "find_in_page"]
            },
            {
                "name": "code_interpreter",
                "description": "Execute code for data analysis",
                "capabilities": ["Python execution", "Data visualization", "Statistical analysis"]
            }
        ]
    }


@router.post("/deep-research/agent")
async def research_with_agent(
    query: str,
    model: Optional[DeepResearchModel] = DeepResearchModel.O3_DEEP_RESEARCH,
    background: bool = True,
    organization_id: str = Depends(verify_organization),
    user_id: str = Depends(get_user_id),
    db: AsyncSession = Depends(get_db),
    token: dict = Depends(get_current_user)
):
    """
    Conduct research using the OpenAI Deep Research Agent.
    
    This endpoint provides a simplified interface to the deep research agent
    with automatic parameter optimization.
    """
    try:
        # Create research agent
        research_agent = OpenAIDeepResearchAgent()
        
        # Prepare context
        context = {
            "organization_id": organization_id,
            "user_id": user_id,
            "research_params": {
                "background": background,
                "prefer_mini": model == DeepResearchModel.O4_MINI_DEEP_RESEARCH
            }
        }
        
        # Process the research request
        result = await research_agent.process_message(query, context)
        
        return {
            "success": True,
            "query": query,
            "model": model.value,
            "result": result,
            "agent": "OpenAI Deep Research Agent"
        }
        
    except Exception as e:
        logger.error(f"Error in agent research endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to conduct agent research: {str(e)}"
        )


@router.get("/deep-research/examples")
async def get_research_examples(
    token: dict = Depends(get_current_user)
):
    """Get example research queries and use cases."""
    return {
        "examples": [
            {
                "category": "Market Research",
                "query": "Research the economic impact of AI automation on the healthcare industry",
                "description": "Comprehensive market analysis with economic data",
                "recommended_model": "o3-deep-research"
            },
            {
                "category": "Competitive Intelligence", 
                "query": "Analyze Tesla's competitive positioning in the electric vehicle market",
                "description": "Competitor analysis with market share data",
                "recommended_model": "o3-deep-research"
            },
            {
                "category": "Legal Research",
                "query": "Research recent developments in AI regulation and compliance requirements",
                "description": "Legal and regulatory analysis",
                "recommended_model": "o3-deep-research"
            },
            {
                "category": "Technology Trends",
                "query": "Identify emerging trends in quantum computing applications",
                "description": "Technology trend analysis",
                "recommended_model": "o4-mini-deep-research"
            },
            {
                "category": "Scientific Research",
                "query": "Research the latest developments in CRISPR gene editing technology",
                "description": "Scientific literature review",
                "recommended_model": "o3-deep-research"
            }
        ],
        "best_practices": [
            "Be specific about what information you need",
            "Include context about your industry or use case",
            "Specify if you need recent data or historical analysis",
            "Mention preferred sources (academic, industry, news, etc.)",
            "Include any constraints or focus areas"
        ],
        "tips": [
            "Use o3-deep-research for comprehensive, complex research",
            "Use o4-mini-deep-research for faster, focused research",
            "Background mode is recommended for complex research",
            "Include specific metrics or data points you need",
            "Specify geographic regions if relevant"
        ]
    }


async def _conduct_background_research(
    research_agent: OpenAIDeepResearchAgent,
    query: str,
    context: Dict[str, Any],
    task_id: str
):
    """Conduct research in background mode."""
    try:
        logger.info(f"Starting background research task: {task_id}")
        result = await research_agent.process_message(query, context)
        
        # Store result (implement result storage service)
        # For now, just log completion
        logger.info(f"Background research completed: {task_id}")
        
    except Exception as e:
        logger.error(f"Background research failed: {task_id}, error: {e}")
