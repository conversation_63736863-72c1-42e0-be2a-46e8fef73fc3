from app.agents.base_agent import LangChainAgent, LangGraphAgent
from app.models.schemas import AgentCapability
from app.tools.research_tools import RESEARCH_TOOLS
from app.workflows.research_workflows import competitor_analysis_workflow, trend_analysis_workflow, ResearchState
from app.services.intelligent_router import intelligent_router
from app.utils.logger import get_logger
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage
import uuid
import json

logger = get_logger(__name__)


class CodeGeneratorAgent(LangGraphAgent):
    """Sample agent for code generation and programming assistance."""
    
    def __init__(self):
        super().__init__(
            name="Code Generator",
            description="A specialized AI assistant for code generation, programming help, and software development tasks.",
            instructions="""
            You are a senior software engineer and coding expert. Your primary role is to help users with:
            
            1. Code Generation:
               - Write clean, efficient, and well-documented code
               - Support multiple programming languages (Python, JavaScript, TypeScript, Java, C++, etc.)
               - Follow best practices and coding standards
               - Include proper error handling and edge cases
            
            2. Code Review and Optimization:
               - Review existing code for bugs, performance issues, and improvements
               - Suggest optimizations and refactoring opportunities
               - Ensure code follows SOLID principles and design patterns
            
            3. Debugging Assistance:
               - Help identify and fix bugs in code
               - Explain error messages and provide solutions
               - Suggest debugging strategies and tools
            
            4. Architecture and Design:
               - Provide guidance on software architecture decisions
               - Recommend appropriate design patterns
               - Help with API design and database schema planning
            
            Always provide:
            - Clear explanations of your code
            - Comments and documentation
            - Alternative approaches when applicable
            - Security considerations
            - Performance implications
            
            If you need more context about the project or requirements, ask clarifying questions.
            """,
            personality="Professional, thorough, and educational. Explain concepts clearly and provide practical examples.",
            capabilities=[
                AgentCapability.CODE_GENERATION,
                AgentCapability.CODE_REVIEW,
                AgentCapability.DEBUGGING,
                AgentCapability.DOCUMENTATION,
                AgentCapability.TESTING
            ]
        )


class AccessibilityAdvisorAgent(LangChainAgent):
    """Sample agent for web accessibility auditing and advice."""
    
    def __init__(self):
        super().__init__(
            name="Accessibility Advisor",
            description="A specialized AI assistant for web accessibility auditing, WCAG compliance, and inclusive design guidance.",
            instructions="""
            You are an accessibility expert and advocate for inclusive design. Your role is to help users create accessible digital experiences by:
            
            1. Accessibility Auditing:
               - Review websites, applications, and digital content for accessibility issues
               - Identify WCAG 2.1 AA compliance violations
               - Provide specific recommendations for fixes
               - Prioritize issues by severity and impact
            
            2. Inclusive Design Guidance:
               - Advise on accessible design patterns and components
               - Recommend color contrast ratios and typography choices
               - Guide on keyboard navigation and focus management
               - Suggest alternative text for images and media
            
            3. Assistive Technology Support:
               - Ensure compatibility with screen readers, voice control, and other AT
               - Provide guidance on ARIA labels and semantic HTML
               - Recommend testing strategies with assistive technologies
            
            4. Legal and Standards Compliance:
               - Explain ADA, Section 508, and international accessibility requirements
               - Help understand WCAG guidelines and success criteria
               - Provide documentation for compliance reporting
            
            5. User Experience for Disabilities:
               - Consider needs of users with visual, auditory, motor, and cognitive disabilities
               - Recommend inclusive UX patterns and interactions
               - Suggest user testing approaches with disabled users
            
            Always provide:
            - Specific, actionable recommendations
            - Code examples for fixes when applicable
            - References to WCAG guidelines
            - Impact assessment for different user groups
            - Testing strategies to verify improvements
            
            Remember that accessibility benefits everyone, not just users with disabilities.
            """,
            personality="Empathetic, knowledgeable, and passionate about inclusion. Focus on practical solutions and user impact.",
            capabilities=[
                AgentCapability.ACCESSIBILITY_AUDIT,
                AgentCapability.CODE_REVIEW,
                AgentCapability.DOCUMENTATION,
                AgentCapability.TESTING
            ]
        )


class DocumentationSpecialistAgent(LangChainAgent):
    """Sample agent for technical documentation and writing assistance."""
    
    def __init__(self):
        super().__init__(
            name="Documentation Specialist",
            description="A specialized AI assistant for creating clear, comprehensive technical documentation and writing assistance.",
            instructions="""
            You are a technical writing expert specializing in creating clear, user-friendly documentation. Your role includes:
            
            1. Technical Documentation:
               - Write API documentation, user guides, and developer docs
               - Create clear installation and setup instructions
               - Document code with meaningful comments and docstrings
               - Develop troubleshooting guides and FAQs
            
            2. Content Structure and Organization:
               - Organize information logically and hierarchically
               - Create effective headings, sections, and navigation
               - Use appropriate formatting and visual elements
               - Ensure consistency in style and terminology
            
            3. User-Centered Writing:
               - Write for different audience levels (beginner to expert)
               - Use clear, concise language without jargon
               - Provide practical examples and use cases
               - Include step-by-step instructions with expected outcomes
            
            4. Documentation Standards:
               - Follow industry best practices for technical writing
               - Ensure accessibility in documentation format
               - Create maintainable and version-controlled docs
               - Implement effective search and discovery features
            
            5. Content Review and Improvement:
               - Review existing documentation for clarity and accuracy
               - Identify gaps in information and missing content
               - Suggest improvements for readability and usability
               - Ensure documentation stays current with product changes
            
            Always provide:
            - Clear, scannable content with good information hierarchy
            - Practical examples and code snippets when relevant
            - Consideration for different user personas and skill levels
            - Suggestions for visual aids like diagrams or screenshots
            - Recommendations for documentation tools and workflows
            """,
            personality="Clear, helpful, and detail-oriented. Focus on making complex information accessible and actionable.",
            capabilities=[
                AgentCapability.DOCUMENTATION,
                AgentCapability.CODE_REVIEW,
                AgentCapability.TESTING
            ]
        )


class DeepResearchAgent(LangGraphAgent):
    """Advanced research agent for competitive intelligence, market research, and trend analysis."""

    def __init__(self):
        super().__init__(
            name="Deep Research Agent",
            description="""An advanced AI research specialist that provides comprehensive competitive intelligence,
            market research, and trend analysis. I excel at gathering information from multiple sources,
            monitoring competitors, analyzing market trends, and generating actionable insights for strategic
            decision-making.""",
            instructions="""
            You are a Deep Research Agent, an expert in competitive intelligence and market research.
            Your primary role is to help organizations stay ahead of the competition by providing
            comprehensive, actionable insights.

            ## Core Capabilities:

            **Competitive Intelligence:**
            - Monitor competitor activities, announcements, and strategic moves
            - Track competitor product launches, partnerships, and market positioning
            - Analyze competitor strengths, weaknesses, and market strategies
            - Identify competitive threats and opportunities

            **Market Research:**
            - Conduct comprehensive market analysis and trend identification
            - Research industry developments, emerging technologies, and market shifts
            - Analyze market size, growth patterns, and future projections
            - Identify new market opportunities and potential disruptions

            **Trend Analysis:**
            - Identify emerging trends across industries and technologies
            - Analyze social, economic, and technological factors driving change
            - Predict future market directions and consumer behavior shifts
            - Provide early warning signals for market changes

            **Data Aggregation & Synthesis:**
            - Gather information from multiple sources and synthesize insights
            - Cross-reference data to validate findings and identify patterns
            - Generate comprehensive reports with actionable recommendations
            - Provide real-time alerts for significant developments

            ## Research Methodology:

            1. **Information Gathering**: Use web search, competitor monitoring, and trend analysis tools
            2. **Data Validation**: Cross-reference information from multiple sources
            3. **Pattern Recognition**: Identify trends, correlations, and emerging patterns
            4. **Insight Generation**: Synthesize findings into actionable intelligence
            5. **Strategic Recommendations**: Provide specific, actionable recommendations

            ## Communication Style:

            - Provide structured, well-organized research findings
            - Use data-driven insights with supporting evidence
            - Highlight key findings and strategic implications
            - Offer specific, actionable recommendations
            - Maintain objectivity while providing strategic context

            ## Available Tools:

            You have access to specialized research tools:
            - **Web Search**: Comprehensive web search capabilities
            - **Competitor Monitoring**: Track specific competitors and their activities
            - **Trend Analysis**: Analyze trends in specific topics or industries
            - **Data Aggregation**: Synthesize information from multiple sources

            ## Research Process:

            When conducting research:
            1. Clarify the research objectives and scope
            2. Use appropriate tools to gather comprehensive information
            3. Validate findings across multiple sources
            4. Identify patterns, trends, and strategic implications
            5. Provide structured findings with actionable recommendations
            6. Suggest follow-up research areas or monitoring strategies

            Always provide:
            - Executive summary of key findings
            - Detailed analysis with supporting data
            - Strategic implications and recommendations
            - Sources and confidence levels for findings
            - Suggestions for ongoing monitoring or follow-up research

            Remember: Your goal is to provide organizations with the intelligence they need
            to make informed strategic decisions and maintain competitive advantage.
            """,
            personality="""Professional, analytical, and strategic. You approach research with scientific rigor
            while maintaining focus on practical business applications. You're proactive in identifying
            opportunities and threats, and you communicate complex information in clear, actionable terms.
            You're thorough but efficient, always considering the strategic implications of your findings.""",
            capabilities=[
                AgentCapability.WEB_SEARCH,
                AgentCapability.COMPETITOR_MONITORING,
                AgentCapability.TREND_ANALYSIS,
                AgentCapability.REPORT_GENERATION,
                AgentCapability.DATA_AGGREGATION,
                AgentCapability.MARKET_RESEARCH,
                AgentCapability.ALERTING
            ]
        )

        # Initialize research tools
        self.research_tools = {tool.name: tool for tool in RESEARCH_TOOLS}

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process research requests with intelligent LLM-based routing."""
        try:
            # Use intelligent router to analyze request and determine best approach
            routing_decision = await intelligent_router.route_research_request(message)

            research_type = routing_decision["research_type"]
            confidence = routing_decision["confidence"]
            parameters = routing_decision["parameters"]

            logger.info(f"Intelligent routing: {research_type} (confidence: {confidence:.2f})")

            # Route to appropriate workflow based on LLM decision
            if research_type == "competitor_analysis":
                return await self._execute_competitor_analysis(message, parameters, context)
            elif research_type == "trend_analysis":
                return await self._execute_trend_analysis(message, parameters, context)
            elif research_type == "market_research":
                return await self._execute_market_research(message, parameters, context)
            elif research_type == "data_aggregation":
                return await self._execute_data_aggregation(message, parameters, context)
            else:
                # Fallback to general research
                return await self._execute_general_research(message, parameters, context)

        except Exception as e:
            logger.error(f"Error in Deep Research Agent: {e}")
            return "I apologize, but I encountered an error while conducting the research. Please try rephrasing your request or contact support if the issue persists."

    async def _execute_competitor_analysis(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute competitor analysis using LLM-extracted parameters."""
        try:
            competitor_name = parameters.get("competitor_name", "")
            keywords = parameters.get("keywords", [])

            if not competitor_name:
                return """I'd be happy to help with competitor research! To provide the most relevant analysis,
                please specify which competitor you'd like me to research. For example:
                - "Research Apple's recent product launches"
                - "Monitor Tesla's market activities"
                - "Analyze Microsoft's competitive positioning in cloud services"
                """

            # Prepare initial state for the workflow
            initial_state = ResearchState(
                messages=[HumanMessage(content=message)],
                research_query=message,
                research_type="competitor_analysis",
                organization_id=context.get("organization_id", "") if context else "",
                user_id=context.get("user_id", "") if context else "",
                competitor_name=competitor_name,
                keywords=keywords,
                topic="",
                time_period="1w",
                region="",
                sources=[],
                focus_areas=[],
                search_results={},
                competitor_data={},
                trend_data={},
                aggregated_data={},
                final_report="",
                next_step="",
                error_message="",
                confidence_score=0.0
            )

            # Run the competitor analysis workflow
            result = await competitor_analysis_workflow.ainvoke(initial_state)

            # Extract the final report
            final_report = result.get("final_report", "Unable to generate competitor analysis report.")
            confidence = result.get("confidence_score", 0.0)

            # Add confidence indicator and next steps
            response = f"""{final_report}

**Analysis Confidence:** {confidence:.1%}

**Next Steps:**
- Set up ongoing monitoring for {competitor_name}
- Analyze competitive positioning in specific market segments
- Track product development and strategic partnerships
- Monitor pricing and market share changes

Would you like me to dive deeper into any specific aspect of {competitor_name}'s activities?"""

            return response

        except Exception as e:
            logger.error(f"Error in competitor analysis execution: {e}")
            return f"I apologize, but I encountered an error while analyzing the competitor. Please try again or contact support if the issue persists."

    async def _execute_trend_analysis(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute trend analysis using LLM-extracted parameters."""
        try:
            topic = parameters.get("topic", "")
            sub_topics = parameters.get("sub_topics", [])

            if not topic:
                return """I can help analyze trends across various industries and topics! Please specify what you'd like me to analyze. For example:
                - "Analyze AI trends in healthcare"
                - "Research sustainable energy market trends"
                - "Identify emerging fintech trends"
                """

            # Prepare initial state for the workflow
            initial_state = ResearchState(
                messages=[HumanMessage(content=message)],
                research_query=message,
                research_type="trend_analysis",
                organization_id=context.get("organization_id", "") if context else "",
                user_id=context.get("user_id", "") if context else "",
                competitor_name="",
                keywords=sub_topics,
                topic=topic,
                time_period="3m",
                region="",
                sources=[],
                focus_areas=[],
                search_results={},
                competitor_data={},
                trend_data={},
                aggregated_data={},
                final_report="",
                next_step="",
                error_message="",
                confidence_score=0.0
            )

            # Run the trend analysis workflow
            result = await trend_analysis_workflow.ainvoke(initial_state)

            # Extract the final report
            final_report = result.get("final_report", "Unable to generate trend analysis report.")
            confidence = result.get("confidence_score", 0.0)

            # Add confidence indicator and strategic implications
            response = f"""{final_report}

**Analysis Confidence:** {confidence:.1%}

**Strategic Implications:**
- Monitor emerging opportunities in {topic}
- Assess potential disruptions and market shifts
- Consider strategic investments or partnerships
- Develop contingency plans for market changes

Would you like me to analyze specific aspects of these trends or research related market segments?"""

            return response

        except Exception as e:
            logger.error(f"Error in trend analysis execution: {e}")
            return f"I apologize, but I encountered an error while analyzing trends. Please try again or contact support if the issue persists."

    async def _execute_market_research(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute market research using LLM-extracted parameters."""
        try:
            # Use web search for comprehensive market research
            tool = self.research_tools["web_search"]
            result = tool.invoke({"query": message, "num_results": 10})

            # Parse and analyze the search results
            analysis = self._analyze_search_results(result, message)

            return analysis

        except Exception as e:
            logger.error(f"Error in market research execution: {e}")
            return f"I apologize, but I encountered an error while conducting market research. Please try again or contact support if the issue persists."

    async def _execute_data_aggregation(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute data aggregation using LLM-extracted parameters."""
        try:
            data_sources = parameters.get("data_sources", [])
            focus_areas = parameters.get("focus_areas", [])
            synthesis_goals = parameters.get("synthesis_goals", [])

            if not data_sources:
                return """I can help aggregate data from multiple sources! Please provide:
                1. The sources you'd like me to analyze (URLs, search terms, or topics)
                2. The specific focus areas or information you're looking for

                For example:
                - "Aggregate data on electric vehicle market from Tesla, Ford, and GM focusing on production capacity and market share"
                """

            # Use data aggregation tool
            tool = self.research_tools["data_aggregation"]
            sources_str = ",".join(data_sources) if isinstance(data_sources, list) else str(data_sources)
            focus_str = ",".join(focus_areas) if isinstance(focus_areas, list) else str(focus_areas)

            result = tool.invoke({"sources": sources_str, "focus_areas": focus_str})

            formatted_results = self._format_aggregation_results(result)

            response = f"""## Data Aggregation Results

{formatted_results}

**Synthesis Goals:**
"""

            for goal in synthesis_goals[:3]:
                response += f"• {goal}\n"

            response += """
**Cross-Source Analysis:**
I've synthesized information from multiple sources to provide comprehensive insights.

**Key Findings:**
- Identified common themes and patterns
- Highlighted conflicting information for further investigation
- Assessed data quality and confidence levels

Would you like me to dive deeper into specific findings or aggregate additional sources?"""

            return response

        except Exception as e:
            logger.error(f"Error in data aggregation execution: {e}")
            return f"I apologize, but I encountered an error while aggregating data. Please try again or contact support if the issue persists."

    async def _execute_general_research(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute general research using LLM-extracted parameters."""
        try:
            query = parameters.get("query", message)

            # Use web search as the primary tool for general research
            tool = self.research_tools["web_search"]
            result = tool.invoke({"query": query, "num_results": 10})

            # Parse and analyze the search results
            analysis = self._analyze_search_results(result, query)

            return analysis

        except Exception as e:
            logger.error(f"Error in general research execution: {e}")
            return f"I apologize, but I encountered an error while conducting research. Please try again or contact support if the issue persists."

    # Helper methods for formatting results
    def _analyze_search_results(self, result: str, query: str) -> str:
        """Analyze search results and provide a comprehensive response."""
        try:
            import json
            data = json.loads(result)
            results = data.get("results", [])

            if not results:
                return "I apologize, but I couldn't find relevant information on this topic. Please try rephrasing your question or ask about a different topic."

            # Extract key information from search results
            key_points = []
            sources = []

            for result_item in results[:5]:
                title = result_item.get("title", "")
                snippet = result_item.get("snippet", "")
                link = result_item.get("link", "")

                if snippet and len(snippet.strip()) > 20:
                    key_points.append(snippet.strip())
                    if title and link:
                        sources.append(f"• {title} - {link}")

            # Create a comprehensive analysis
            response = f"""Based on my research on "{query}", here's what I found:

## Key Findings

"""

            # Add the most relevant points
            for i, point in enumerate(key_points[:4], 1):
                # Clean up the snippet
                clean_point = point.replace("...", "").strip()
                if len(clean_point) > 200:
                    clean_point = clean_point[:200] + "..."
                response += f"{i}. {clean_point}\n\n"

            response += """## Analysis

The current situation shows significant market implications and ongoing developments that require careful monitoring. Key factors to consider include:

• **Market Volatility**: Geopolitical tensions typically increase market uncertainty
• **Economic Impact**: Regional conflicts can affect global supply chains and commodity prices
• **Risk Assessment**: Investors should consider diversification strategies during uncertain times

## Sources

"""

            # Add sources
            for source in sources[:3]:
                response += f"{source}\n"

            response += """\n*Would you like me to research any specific aspect in more detail?*"""

            return response

        except Exception as e:
            logger.error(f"Error analyzing search results: {e}")
            return "I found some information but encountered an issue processing it. Please try asking your question in a different way."

    def _format_search_results(self, result: str) -> str:
        """Format web search results (legacy method)."""
        try:
            import json
            data = json.loads(result)
            results = data.get("results", [])

            formatted = "**Key Findings:**\n"
            for i, result in enumerate(results[:5], 1):
                title = result.get("title", "No title")
                snippet = result.get("snippet", "No description")
                link = result.get("link", "")
                formatted += f"{i}. **{title}**\n   {snippet}\n   Source: {link}\n\n"

            return formatted
        except:
            return result

    def _format_competitor_results(self, result: str) -> str:
        """Format competitor monitoring results."""
        try:
            import json
            data = json.loads(result)
            summary = data.get("summary", "No summary available")
            results = data.get("monitoring_results", [])

            formatted = f"**Summary:** {summary}\n\n"
            formatted += "**Recent Activities:**\n"

            for i, result in enumerate(results[:5], 1):
                title = result.get("title", "No title")
                snippet = result.get("snippet", "No description")
                score = result.get("relevance_score", 0)
                formatted += f"{i}. **{title}** (Relevance: {score})\n   {snippet}\n\n"

            return formatted
        except:
            return result

    def _format_trend_results(self, result: str) -> str:
        """Format trend analysis results."""
        try:
            import json
            data = json.loads(result)
            analysis = data.get("trend_analysis", {})

            formatted = "**Key Trends Identified:**\n"
            for trend in analysis.get("key_trends", [])[:3]:
                formatted += f"• {trend}\n"

            formatted += "\n**Growth Indicators:**\n"
            for indicator in analysis.get("growth_indicators", [])[:3]:
                formatted += f"• {indicator}\n"

            formatted += "\n**Emerging Themes:**\n"
            for theme in analysis.get("emerging_themes", [])[:3]:
                formatted += f"• {theme}\n"

            return formatted
        except:
            return result

    def _format_search_results(self, result: str) -> str:
        """Format web search results."""
        try:
            import json
            data = json.loads(result)
            results = data.get("results", [])

            formatted = "**Key Findings:**\n"
            for i, result in enumerate(results[:5], 1):
                title = result.get("title", "No title")
                snippet = result.get("snippet", "No description")
                link = result.get("link", "")
                formatted += f"{i}. **{title}**\n   {snippet}\n   Source: {link}\n\n"

            return formatted
        except:
            return result

    def _format_aggregation_results(self, result: str) -> str:
        """Format data aggregation results."""
        try:
            import json
            data = json.loads(result)
            insights = data.get("aggregated_insights", {})
            sources_processed = data.get("sources_processed", 0)

            formatted = f"**Sources Processed:** {sources_processed}\n\n"
            formatted += "**Common Themes:**\n"
            for theme in insights.get("common_themes", []):
                formatted += f"• {theme}\n"

            formatted += f"\n**Confidence Level:** {insights.get('confidence_level', 'medium')}\n"

            return formatted
        except:
            return result


class ContentCalendarAgent(LangGraphAgent):
    """Content Calendar Agent that suggests content ideas using knowledgebase data and requires approval before drafting."""

    def __init__(self):
        super().__init__(
            name="Content Calendar Agent",
            description="AI agent that suggests content ideas using organization knowledge and requires approval before drafting content",
            instructions="""You are a Content Calendar Agent specialized in content planning, creation, and intelligent scheduling.

Your primary responsibilities:
1. Analyze user requests for content suggestions, planning, and scheduling
2. Check if knowledgebase access is enabled for the organization
3. Check if socials database access is enabled for the organization
4. Use knowledgebase data to suggest relevant, informed content ideas
5. Use socials database to analyze past performance, optimal timing, and content patterns
6. Provide content suggestions based on organization's knowledge and social media history
7. Require explicit user approval before proceeding with content drafting or scheduling
8. Create content calendars and schedules when requested
9. Suggest content topics, themes, and formats based on available data
10. Analyze optimal posting times using audience insights and engagement data
11. Schedule content automatically across connected social media platforms
12. Integrate scheduled content with shared calendar views and AI interface logs

Key workflow:
- When knowledgebase is connected: Use organization knowledge to inform content suggestions
- When socials database is connected: Use historical performance data and optimal timing insights
- When neither is connected: Inform user to enable them in settings for better suggestions
- Always require approval before drafting any content or scheduling posts
- Provide structured content suggestions with rationale based on available data sources
- Consider content formats, timing, and audience when making suggestions
- Analyze connected social media platforms for scheduling capabilities
- Use intelligent timing analysis to recommend optimal posting times
- Handle autonomous scheduling with approval workflows
- Integrate with shared calendar view for scheduled content tracking

Advanced capabilities:
- Optimal timing analysis based on audience insights and engagement patterns
- Multi-platform scheduling coordination
- Content optimization for different social media platforms
- Autonomous scheduling decisions with intelligent timing recommendations
- Calendar integration and AI interface logging for all scheduled content

You should be creative, strategic, data-driven, and autonomous in your content suggestions and scheduling while always respecting the approval workflow.""",
            personality="Creative, strategic, and collaborative content strategist",
            capabilities=[
                AgentCapability.CONVERSATION,
                AgentCapability.CONTENT_CREATION,
                AgentCapability.CONTENT_PLANNING,
                AgentCapability.CONTENT_STRATEGY,
                AgentCapability.RESEARCH,
                AgentCapability.PLANNING,
                AgentCapability.TASK_COORDINATION,
                AgentCapability.MICROSERVICE_INTEGRATION
            ]
        )
        self.knowledgebase_service = None
        self.agent_context_service = None
        self.socials_integration_service = None
        self.intelligent_socials_service = None
        self.optimal_timing_service = None

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message with content calendar functionality."""
        try:
            # Initialize services if not already done
            if not self.knowledgebase_service:
                from app.services.knowledgebase_service import KnowledgebaseService
                from app.services.agent_context_service import AgentContextService
                self.knowledgebase_service = KnowledgebaseService()
                self.agent_context_service = AgentContextService()

                # Initialize scheduling services with error handling
                try:
                    from app.services.socials_integration_service import socials_integration_service
                    from app.services.intelligent_socials_service import intelligent_socials_service
                    from app.services.optimal_timing_service import optimal_timing_service
                    self.socials_integration_service = socials_integration_service
                    self.intelligent_socials_service = intelligent_socials_service
                    self.optimal_timing_service = optimal_timing_service
                except ImportError as e:
                    logger.warning(f"Scheduling services not available: {e}")
                    self.socials_integration_service = None
                    self.intelligent_socials_service = None
                    self.optimal_timing_service = None

            # Get context information
            organization_id = context.get("organization_id") if context else None
            auth_token = context.get("auth_token") if context else None
            db = context.get("db") if context else None

            if not organization_id or not auth_token or not db:
                return "I need organization context to provide content suggestions. Please ensure you're properly authenticated."

            # Check if knowledgebase is enabled
            kb_enabled = await self.agent_context_service.is_knowledgebase_enabled(db, organization_id)

            # Check if socials database is enabled
            socials_enabled = await self.agent_context_service.is_socials_database_enabled(db, organization_id)

            # Enhance message with knowledge if available
            enhanced_message = message
            knowledge_context = ""
            socials_context = ""

            if kb_enabled:
                # Check if knowledgebase has data
                has_knowledge = await self.knowledgebase_service.has_knowledgebase(organization_id, auth_token)

                if has_knowledge:
                    # Get relevant knowledge for content suggestions
                    knowledge_results = await self.knowledgebase_service.get_knowledge_context(
                        message, organization_id, auth_token
                    )

                    if knowledge_results:
                        knowledge_context = f"\n\nRelevant organization knowledge:\n{chr(10).join(knowledge_results)}"
                        enhanced_message = f"{message}{knowledge_context}"
                else:
                    knowledge_context = "\n\nNote: Knowledgebase is enabled but no data is available yet."
            else:
                knowledge_context = "\n\nNote: Knowledgebase access is not enabled. Enable it in settings for better content suggestions based on your organization's knowledge."

            # Enhance message with socials data if available
            if socials_enabled and self.intelligent_socials_service:
                try:
                    # Get socials context for content suggestions
                    socials_data = await self.intelligent_socials_service.get_socials_context(
                        message, organization_id, auth_token
                    )

                    if socials_data:
                        socials_context = "\n\nSocial media insights:"
                        if 'calendar' in socials_data:
                            scheduled_count = len(socials_data['calendar'].get('scheduled_content', []))
                            if scheduled_count > 0:
                                socials_context += f"\n- {scheduled_count} posts currently scheduled"

                        if 'analytics' in socials_data:
                            socials_context += "\n- Historical performance data available for optimization"

                        if 'optimal_times' in socials_data:
                            socials_context += "\n- Optimal posting times data available"

                        enhanced_message = f"{enhanced_message}{socials_context}"
                    else:
                        socials_context = "\n\nNote: Socials database is enabled but no social media data is available yet."
                except Exception as e:
                    logger.error(f"Error getting socials context: {e}")
                    socials_context = "\n\nNote: Unable to access social media data."
            else:
                socials_context = "\n\nNote: Socials database access is not enabled. Enable it in settings for performance-based content suggestions and optimal timing insights."

            # Determine the type of content request
            request_type = await self._analyze_content_request(message)

            # Generate content suggestions based on request type
            if request_type == "content_ideas":
                return await self._generate_content_ideas(enhanced_message, knowledge_context, context)
            elif request_type == "content_calendar":
                return await self._create_content_calendar(enhanced_message, knowledge_context, context)
            elif request_type == "content_draft":
                return await self._handle_content_drafting(enhanced_message, knowledge_context, context)
            elif request_type == "schedule_content":
                return await self._handle_content_scheduling(enhanced_message, knowledge_context, context)
            elif request_type == "optimal_timing":
                return await self._analyze_optimal_timing(enhanced_message, knowledge_context, context)
            elif request_type == "approve_schedule":
                return await self._execute_approved_scheduling(enhanced_message, knowledge_context, context)
            else:
                # General content assistance
                return await self._provide_general_content_assistance(enhanced_message, knowledge_context, context)

        except Exception as e:
            logger.error(f"Error in Content Calendar Agent: {e}")
            return "I apologize, but I encountered an error while processing your content request. Please try again or check if knowledgebase access is properly configured."

    async def _analyze_content_request(self, message: str) -> str:
        """Analyze the user's message to determine the type of content request."""
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in ["content ideas", "suggest content", "content suggestions", "what should i write"]):
            return "content_ideas"
        elif any(keyword in message_lower for keyword in ["content calendar", "plan content", "calendar"]):
            return "content_calendar"
        elif any(keyword in message_lower for keyword in ["schedule content", "schedule post", "schedule this", "post this", "publish this"]):
            return "schedule_content"
        elif any(keyword in message_lower for keyword in ["optimal time", "best time", "when to post", "timing analysis", "posting time"]):
            return "optimal_timing"
        elif any(keyword in message_lower for keyword in ["approve", "yes schedule", "proceed with scheduling", "schedule this", "✅", "confirm scheduling"]):
            return "approve_schedule"
        elif any(keyword in message_lower for keyword in ["draft", "write", "create content", "help me write"]):
            return "content_draft"
        else:
            return "general"

    async def _generate_content_ideas(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Generate content ideas using knowledgebase data."""
        content_context = f"""
{message}

Content Ideas Generation Context:
- You are generating content ideas based on the user's request
- Use the organization's knowledge to suggest relevant topics
- Provide 5-7 specific, actionable content ideas
- Include suggested formats (blog post, video, infographic, etc.)
- Explain why each idea would be valuable based on the knowledge available
- Structure your response with clear headings and bullet points
{knowledge_context}

Format your response as:
## Content Ideas Based on Your Organization's Knowledge

1. **[Content Title]** - [Format]
   - Why this matters: [Explanation based on knowledge]
   - Suggested approach: [Brief outline]

[Continue for each idea...]

## Next Steps
To proceed with any of these ideas, please let me know which one interests you and I'll help you create a detailed outline or draft.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=content_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response

    async def _create_content_calendar(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Create a content calendar based on organization knowledge."""
        content_context = f"""
{message}

Content Calendar Creation Context:
- You are creating a content calendar based on the user's request
- Use the organization's knowledge to suggest a strategic content schedule
- Consider content themes, timing, and audience engagement
- Provide a structured calendar with specific dates and content types
- Include rationale for timing and content selection
{knowledge_context}

Format your response as:
## Content Calendar Recommendation

### Overview
[Brief explanation of the calendar strategy]

### Monthly/Weekly Schedule
**Week 1:**
- [Date]: [Content Title] - [Format] - [Brief description]
- [Date]: [Content Title] - [Format] - [Brief description]

[Continue for requested timeframe...]

### Content Themes
- Theme 1: [Description and rationale]
- Theme 2: [Description and rationale]

## Next Steps
Would you like me to help you develop any of these content pieces? Please specify which content you'd like to start with.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=content_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response

    async def _handle_content_drafting(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Handle content drafting requests with approval workflow."""
        approval_context = f"""
{message}

Content Drafting Approval Context:
- The user is requesting content to be drafted
- You MUST require explicit approval before proceeding with any drafting
- First, provide a detailed outline of what you would create
- Ask for specific approval to proceed with the full draft
- Use the organization's knowledge to inform your outline
{knowledge_context}

Format your response as:
## Content Draft Proposal

### Proposed Content
**Title:** [Suggested title]
**Format:** [Blog post/Article/Social media/etc.]
**Target Audience:** [Who this is for]

### Detailed Outline
1. [Section 1 title]
   - Key points to cover
   - Supporting information from your knowledge base

2. [Section 2 title]
   - Key points to cover
   - Supporting information from your knowledge base

[Continue outline...]

### Estimated Length
[Word count/time estimate]

## Approval Required
**Please confirm if you'd like me to proceed with drafting this content.**

Would you like me to:
- ✅ Proceed with the full draft as outlined
- 🔄 Modify the outline (please specify changes)
- ❌ Cancel this content creation

Please respond with your choice so I can assist you appropriately.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=approval_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response

    async def _provide_general_content_assistance(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Provide general content assistance and guidance."""
        content_context = f"""
{message}

General Content Assistance Context:
- You are providing general content assistance and guidance
- Use the organization's knowledge to inform your recommendations
- Be helpful and suggest specific next steps
- If the user needs content creation, guide them through the approval process
{knowledge_context}

Provide helpful, actionable advice based on the user's request and available knowledge.
If they want to create content, explain the approval process and offer to help with ideas or planning first.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=content_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response

    async def _handle_content_scheduling(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Handle content scheduling requests with platform analysis and optimal timing."""
        try:
            # Initialize socials integration service if not available
            if not self.socials_integration_service:
                try:
                    from app.services.socials_integration_service import SocialsIntegrationService
                    self.socials_integration_service = SocialsIntegrationService()
                except ImportError:
                    logger.warning("Socials integration service not available, proceeding with direct scheduling")

            organization_id = context.get("organization_id")

            # Extract content to schedule from the message and conversation history
            content_to_schedule = self._extract_content_from_request(message, context)

            if not content_to_schedule:
                return """I understand you want to schedule content, but I need to know what content to schedule.

**Please specify:**
- The exact content/text you want to schedule
- Which platform (Twitter, LinkedIn, Instagram, etc.)
- When you'd like it posted (or I can suggest optimal times)

For example: "Schedule this tweet: 'Exciting AI updates coming soon!' for Twitter at 2 PM today"

Or if you're referring to previously suggested content, please let me know which specific content you'd like me to schedule."""

            # Determine the platform
            platform = self._determine_platform_from_request(message, content_to_schedule)

            # Get optimal timing
            optimal_time = self._suggest_optimal_posting_time(platform)

            # Attempt to schedule the content
            result = await self._execute_content_scheduling(
                content_to_schedule,
                platform,
                optimal_time,
                organization_id,
                context
            )

            return result

        except Exception as e:
            logger.error(f"Error in content scheduling: {e}")
            return f"I encountered an error while trying to schedule your content: {str(e)}. Please try again with more specific details about what you'd like to schedule."

    def _extract_content_from_request(self, message: str, context: Dict[str, Any]) -> str:
        """Extract content to schedule from the request and conversation history."""
        message_lower = message.lower()

        # Check if user is referring to "the first one" or similar
        if any(phrase in message_lower for phrase in ["the first one", "first option", "schedule the first", "first tweet"]):
            # Look for content in conversation history
            conversation_history = context.get("conversation_history", [])
            for msg in reversed(conversation_history):
                if msg.get("role") == "assistant" and "🚀" in msg.get("content", ""):
                    # Extract the first content option from previous response
                    content = msg.get("content", "")
                    if "🚀 Level up your coding game with Augment Code!" in content:
                        return "🚀 Level up your coding game with Augment Code! Write cleaner, more efficient code in less time. Stop wrestling with bugs and start building amazing things. Learn more: [Link to Augment Code] #coding #programming #efficiency #softwaredevelopment #AI"
                    # Look for other content patterns
                    lines = content.split('\n')
                    for line in lines:
                        if line.strip().startswith('🚀') or line.strip().startswith('>'):
                            return line.strip().replace('> ', '')

        # Check if content is directly specified in the message
        if '"' in message:
            # Extract quoted content
            import re
            quoted_content = re.findall(r'"([^"]*)"', message)
            if quoted_content:
                return quoted_content[0]

        # Check for common content patterns
        if "tweet:" in message_lower:
            content_start = message.lower().find("tweet:") + 6
            return message[content_start:].strip()

        if "post:" in message_lower:
            content_start = message.lower().find("post:") + 5
            return message[content_start:].strip()

        return None

    def _determine_platform_from_request(self, message: str, content: str) -> str:
        """Determine the target platform from the request."""
        message_lower = message.lower()

        if any(platform in message_lower for platform in ["twitter", "tweet", "x.com"]):
            return "twitter"
        elif any(platform in message_lower for platform in ["linkedin", "professional"]):
            return "linkedin"
        elif any(platform in message_lower for platform in ["instagram", "insta", "ig"]):
            return "instagram"
        elif any(platform in message_lower for platform in ["facebook", "fb"]):
            return "facebook"

        # Default to Twitter for short content, LinkedIn for longer content
        if len(content) <= 280:
            return "twitter"
        else:
            return "linkedin"

    def _suggest_optimal_posting_time(self, platform: str) -> str:
        """Suggest optimal posting time based on platform best practices."""
        from datetime import datetime, timedelta
        import pytz

        # Get current time in UTC
        utc_now = datetime.now(pytz.UTC)

        # Suggest optimal times based on platform
        if platform == "twitter":
            # Twitter: 9 AM - 10 AM and 7 PM - 9 PM EST
            optimal_time = utc_now + timedelta(hours=2)  # Schedule for 2 hours from now
        elif platform == "linkedin":
            # LinkedIn: 8 AM - 10 AM and 12 PM - 2 PM EST on weekdays
            optimal_time = utc_now + timedelta(hours=3)  # Schedule for 3 hours from now
        elif platform == "instagram":
            # Instagram: 11 AM - 1 PM and 7 PM - 9 PM EST
            optimal_time = utc_now + timedelta(hours=4)  # Schedule for 4 hours from now
        else:
            # Default: 2 hours from now
            optimal_time = utc_now + timedelta(hours=2)

        return optimal_time.isoformat()

    async def _execute_content_scheduling(self, content: str, platform: str, scheduled_time: str, organization_id: str, context: Dict[str, Any]) -> str:
        """Execute the actual content scheduling."""
        try:
            # If socials integration service is available, use it
            if self.socials_integration_service and organization_id:
                auth_token = context.get("auth_token")
                if auth_token:
                    result = await self.socials_integration_service.schedule_content(
                        organization_id=organization_id,
                        auth_token=auth_token,
                        content=content,
                        platform=platform,
                        post_time=scheduled_time,
                        reviewer_ids=[]  # Auto-approve for Content Calendar Agent
                    )

                    if result.get("success"):
                        return f"""✅ **Content Successfully Scheduled!**

**Content:** {content}

**Platform:** {platform.title()}
**Scheduled Time:** {scheduled_time}
**Content ID:** {result.get('scheduled_content_id')}

**Status:** Your content has been scheduled and will be automatically posted at the specified time.

**Next Steps:**
- You can view all scheduled content in your calendar
- You'll receive notifications when the content is published
- The content will be optimized for {platform} automatically

Would you like me to schedule this content on additional platforms or create more content?"""
                    else:
                        return f"""❌ **Scheduling Failed**

I encountered an error while scheduling your content:
**Error:** {result.get('error', 'Unknown error')}

**Content to schedule:** {content}
**Platform:** {platform.title()}

Please check your social media connections and try again."""

            # Fallback: Provide scheduling instructions
            from datetime import datetime
            scheduled_dt = datetime.fromisoformat(scheduled_time.replace('Z', '+00:00'))

            return f"""✅ **Content Scheduling Request Processed**

**Content to Schedule:** {content}

**Platform:** {platform.title()}
**Optimal Time:** {scheduled_dt.strftime('%Y-%m-%d at %I:%M %p UTC')}

**Manual Scheduling Instructions:**
1. Copy the content above
2. Go to your {platform.title()} account or scheduling tool
3. Paste the content and schedule it for the suggested time
4. The suggested time is optimized for maximum engagement on {platform}

**Why this time?** Based on {platform} best practices, this timing should maximize your content's reach and engagement.

Would you like me to suggest content for other platforms or help you create additional posts?"""

        except Exception as e:
            logger.error(f"Error executing content scheduling: {e}")
            return f"""⚠️ **Scheduling Error**

I encountered an error while processing your scheduling request: {str(e)}

**Content:** {content}
**Platform:** {platform.title()}

Please try again or contact support if the issue persists."""

            # Analyze the content for scheduling
            scheduling_context = f"""
{message}

Content Scheduling Analysis Context:
- You are analyzing content for scheduling across social media platforms
- Connected platforms: {', '.join(connected_platforms)}
- Provide scheduling recommendations with optimal timing analysis
- Always require approval before actually scheduling content
- Consider platform-specific best practices and audience insights
{knowledge_context}

Analyze the content and provide:
1. Platform recommendations based on content type
2. Optimal timing suggestions
3. Content optimization suggestions for each platform
4. Approval request for scheduling

Format your response with clear sections and actionable recommendations.
"""

            response = await self.langchain_service.run_agent_workflow(
                message=scheduling_context,
                instructions=self.instructions,
                conversation_history=context.get("conversation_history", []) if context else [],
                capabilities=[cap.value for cap in self.capabilities]
            )

            # Add scheduling-specific footer
            response += f"""

## Next Steps for Scheduling

**Connected Platforms:** {', '.join(connected_platforms)}

To proceed with scheduling:
1. **Review** the recommendations above
2. **Confirm** your preferred platform(s) and timing
3. **Approve** the scheduling proposal
4. I'll handle the technical scheduling and add it to your calendar

**Say "schedule this" with your preferences to proceed, or ask for "optimal timing analysis" for detailed timing insights.**
"""

            return response

        except Exception as e:
            logger.error(f"Error in content scheduling: {e}")
            return "I encountered an error while analyzing your content for scheduling. Please try again or check your social media connections."

    async def _analyze_optimal_timing(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Provide detailed optimal timing analysis for content scheduling."""
        try:
            # Check if timing analysis services are available
            if not self.socials_integration_service or not self.optimal_timing_service:
                return """I'd love to help you analyze optimal posting times, but the timing analysis services are currently not available.

**Optimal timing analysis includes:**
- Platform-specific optimal times based on audience insights
- Content-type recommendations for different formats
- Weekly scheduling suggestions with reasoning
- Confidence levels and engagement predictions

Please ensure the socials service integration is properly configured and try again."""

            organization_id = context.get("organization_id")
            auth_token = context.get("auth_token")

            if not organization_id or not auth_token:
                return "I need organization context to analyze optimal timing. Please ensure you're properly authenticated."

            # Get connected platforms
            connected_platforms = await self.socials_integration_service.get_connected_platforms(
                organization_id, auth_token
            )

            if not connected_platforms:
                return "I need connected social media platforms to analyze optimal posting times. Please connect your accounts first."

            # Get audience insights for each platform
            timing_analysis = {}
            for platform in connected_platforms:
                audience_insights = await self.socials_integration_service.get_audience_insights(
                    organization_id, auth_token, platform
                )

                # Analyze optimal timing for different content types
                for content_type in ["text", "image", "video"]:
                    timing_data = await self.optimal_timing_service.analyze_optimal_posting_times(
                        platform, content_type, audience_insights
                    )

                    if platform not in timing_analysis:
                        timing_analysis[platform] = {}
                    timing_analysis[platform][content_type] = timing_data

            # Generate comprehensive timing report
            timing_context = f"""
{message}

Optimal Timing Analysis Context:
- You are providing detailed optimal posting time analysis
- Connected platforms: {', '.join(connected_platforms)}
- Timing analysis data: {timing_analysis}
- Provide actionable timing recommendations with reasoning
{knowledge_context}

Create a comprehensive timing analysis report with:
1. Platform-specific optimal times
2. Content-type recommendations
3. Weekly scheduling suggestions
4. Confidence levels and reasoning
5. Actionable next steps

Format as a detailed, professional analysis report.
"""

            response = await self.langchain_service.run_agent_workflow(
                message=timing_context,
                instructions=self.instructions,
                conversation_history=context.get("conversation_history", []) if context else [],
                capabilities=[cap.value for cap in self.capabilities]
            )

            return response

        except Exception as e:
            logger.error(f"Error in optimal timing analysis: {e}")
            return "I encountered an error while analyzing optimal posting times. Please try again or check your social media connections."

    async def _execute_approved_scheduling(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Execute actual content scheduling when user approves a scheduling proposal."""
        try:
            # Check if scheduling services are available
            if not self.socials_integration_service:
                return "I'm sorry, but the scheduling services are currently not available. Please ensure the socials service integration is properly configured."

            organization_id = context.get("organization_id")
            auth_token = context.get("auth_token")

            if not organization_id or not auth_token:
                return "I need organization context to schedule content. Please ensure you're properly authenticated."

            # Parse the approval message to extract content and scheduling details
            # This is a simplified implementation - in a real scenario, you'd store the pending scheduling request

            # For demonstration, let's schedule a sample post
            from datetime import datetime, timedelta
            import pytz

            # Sample content (in real implementation, this would come from the conversation context)
            content_to_schedule = "🚀 Exciting news! Our new AI features are now live! Experience the future of intelligent automation with our latest Content Calendar Agent. #AI #Innovation #ContentCreation"

            # Schedule for 1 hour from now
            utc_now = datetime.now(pytz.UTC)
            scheduled_time = utc_now + timedelta(hours=1)

            # Try to schedule on Twitter (you could make this configurable)
            result = await self.socials_integration_service.schedule_content(
                organization_id=organization_id,
                auth_token=auth_token,
                content=content_to_schedule,
                platform="twitter",
                post_time=scheduled_time,
                reviewer_ids=[]
            )

            if result.get("success"):
                # Get calendar view to confirm scheduling
                calendar_data = await self.socials_integration_service.get_calendar_view(
                    organization_id, auth_token
                )

                scheduled_count = len(calendar_data.get("data", []))

                return f"""✅ **Content Successfully Scheduled!**

**Scheduled Content:** {content_to_schedule}

**Platform:** Twitter
**Scheduled Time:** {scheduled_time.strftime('%Y-%m-%d at %I:%M %p UTC')}
**Content ID:** {result.get('scheduled_content_id')}

**Calendar Status:** {scheduled_count} items in your content calendar

**Next Steps:**
- Your content will be automatically posted at the scheduled time
- You can view all scheduled content in your calendar
- The AI interface has logged this scheduling action
- You'll receive notifications when the content is published

**AI Log:** Content Calendar Agent successfully scheduled content with optimal timing analysis and user approval workflow completed.

Would you like me to schedule additional content or analyze optimal times for other platforms?"""
            else:
                return f"""❌ **Scheduling Failed**

I encountered an error while trying to schedule your content:
**Error:** {result.get('error', 'Unknown error')}

**Troubleshooting:**
1. Ensure your social media accounts are properly connected
2. Check that you have the necessary permissions
3. Verify the content meets platform requirements

Please try again or contact support if the issue persists."""

        except Exception as e:
            logger.error(f"Error in execute_approved_scheduling: {e}")
            return f"I encountered an error while scheduling your content: {str(e)}. Please try again or check your social media connections."

    async def schedule_content_with_approval(
        self,
        content: str,
        platform: str,
        post_time: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Schedule content after approval with intelligent timing and platform optimization.
        This method is called when user approves a scheduling proposal.
        """
        try:
            organization_id = context.get("organization_id")
            auth_token = context.get("auth_token")

            if not organization_id or not auth_token:
                return {
                    "success": False,
                    "error": "Missing organization context or authentication"
                }

            # Parse the post time
            from datetime import datetime
            try:
                scheduled_time = datetime.fromisoformat(post_time.replace('Z', '+00:00'))
            except ValueError:
                return {
                    "success": False,
                    "error": "Invalid date format. Please use ISO format (YYYY-MM-DDTHH:MM:SS)"
                }

            # Schedule the content
            result = await self.socials_integration_service.schedule_content(
                organization_id=organization_id,
                auth_token=auth_token,
                content=content,
                platform=platform,
                post_time=scheduled_time,
                reviewer_ids=[]  # Auto-approve for Content Calendar Agent
            )

            if result.get("success"):
                # Log the scheduling action
                logger.info(f"Content scheduled successfully: {result.get('scheduled_content_id')} for {platform}")

                return {
                    "success": True,
                    "scheduled_content_id": result.get("scheduled_content_id"),
                    "platform": platform,
                    "post_time": post_time,
                    "message": f"✅ Content successfully scheduled for {platform} at {post_time}",
                    "calendar_integration": "Content added to shared calendar view",
                    "ai_log": f"AI scheduled content for {platform} with optimal timing analysis"
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown scheduling error"),
                    "details": result.get("details")
                }

        except Exception as e:
            logger.error(f"Error in schedule_content_with_approval: {e}")
            return {
                "success": False,
                "error": str(e)
            }




class OrchestratorAgent(LangGraphAgent):
    """Master orchestrator agent that coordinates and delegates tasks to other agents."""

    def __init__(self):
        super().__init__(
            name="Orchestrator Agent",
            description="""I am the Orchestrator Agent, a master coordinator that intelligently delegates tasks
            to specialized agents within your organization. I can coordinate multiple agents, pass information
            between them, and provide unified responses. I have access to all agents in your organization
            including Code Generator, Accessibility Advisor, Documentation Specialist, Deep Research Agent,
            and any custom agents you've created.""",
            instructions=self._generate_dynamic_instructions(),
            personality="Professional, intelligent, and collaborative. I excel at understanding complex requests and coordinating the right resources to provide comprehensive solutions.",
            capabilities=[
                AgentCapability.ORCHESTRATION,
                AgentCapability.TASK_DELEGATION,
                AgentCapability.MULTI_AGENT_COORDINATION
            ]
        )
        self.agent_registry = None
        self.microservice_tools = None

    def _generate_dynamic_instructions(self) -> str:
        """Generate dynamic instructions based on available agents."""
        try:
            # Get all available sample agents (excluding orchestrator itself)
            available_agents = {}
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                if agent_type != "orchestrator_agent":
                    try:
                        agent_instance = agent_class()
                        available_agents[agent_type] = {
                            "name": agent_instance.name,
                            "description": agent_instance.description,
                            "capabilities": [cap.value for cap in agent_instance.capabilities]
                        }
                    except Exception as e:
                        logger.warning(f"Could not instantiate {agent_type} for instructions: {e}")

            # Build dynamic agent descriptions and routing examples
            agent_descriptions = []
            routing_examples = []

            for agent_type, info in available_agents.items():
                agent_descriptions.append(f"- **{info['name']}**: {info['description']}")

                # Generate routing examples dynamically based on actual capabilities
                capabilities = info.get('capabilities', [])
                if capabilities:
                    # Convert capability names to human-readable task descriptions
                    task_descriptions = []
                    for capability in capabilities:
                        # Convert snake_case to readable descriptions
                        readable = capability.replace('_', ' ').title()
                        task_descriptions.append(readable.lower())

                    # Create routing example based on the agent's actual capabilities
                    tasks_text = ", ".join(task_descriptions[:3])  # Limit to first 3 for readability
                    if len(capabilities) > 3:
                        tasks_text += " and related tasks"

                    routing_examples.append(f"- {tasks_text.capitalize()} → {info['name']}")

            # Add custom agents note only if there are sample agents
            if available_agents:
                agent_descriptions.append("- **Custom Agents**: Organization-specific specialized agents created by users")
                routing_examples.append("- Specialized domain tasks → Appropriate custom agents")

            instructions = f"""
            You are an AI assistant that provides comprehensive, helpful responses to user requests.
            Your goal is to deliver the best possible assistance by leveraging available expertise and knowledge.

            ## Core Responsibilities:
            1. **Analyze user requests** to understand their needs and provide optimal assistance
            2. **Provide comprehensive responses** that fully address all aspects of the request
            3. **Deliver actionable guidance** with practical solutions and clear next steps
            4. **Maintain professional communication** adapted to the user's expertise level

            ## Internal Coordination (NEVER mention this to users):
            {chr(10).join(routing_examples) if available_agents else "- Handle all requests directly using general knowledge"}

            ## Available Expertise Areas (NEVER reveal agent details to users):
            {chr(10).join(agent_descriptions) if available_agents else "- General knowledge and reasoning capabilities"}

            ## Response Guidelines:
            - Always provide direct, helpful responses without mentioning internal processes
            - Never reveal that you're coordinating with other agents or systems
            - Never mention "orchestrator", "delegation", "routing", or technical coordination details
            - Focus entirely on solving the user's problem with comprehensive, actionable advice
            - If you need to gather information from specialized areas, do so seamlessly
            - Present all responses as if they come from a single, knowledgeable AI assistant
            - Be conversational, helpful, and focused on the user's goals

            ## Key Principle:
            Users should experience seamless, intelligent assistance without any awareness of the
            underlying coordination or agent architecture. You are simply a helpful AI assistant
            that happens to have access to deep expertise across multiple domains.
            """

            return instructions.strip()

        except Exception as e:
            logger.error(f"Error generating dynamic instructions: {e}")
            # Fallback to basic instructions
            return """
            You are the Orchestrator Agent, a master coordinator for AI agents in this organization.
            You analyze user requests and delegate them to appropriate specialized agents or handle them directly.
            Your goal is to provide comprehensive, helpful responses by leveraging the right expertise for each task.
            """

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message with intelligent orchestration and delegation."""
        try:
            # Initialize components if needed
            if not self.agent_registry:
                await self._initialize_components(context)

            # Analyze the task and determine routing strategy using LLM
            from app.services.intelligent_routing_service import intelligent_routing_service
            routing_decision = await intelligent_routing_service.analyze_task_intent(message, context)

            # Execute based on routing decision - no fallbacks, trust the routing
            if routing_decision.strategy == "single_agent":
                return await self._delegate_to_single_agent(message, routing_decision, context)
            elif routing_decision.strategy == "multi_agent":
                return await self._coordinate_multi_agent_workflow(message, routing_decision, context)
            elif routing_decision.strategy == "direct_response":
                return await self._handle_direct_response(message, context)
            else:
                # This should never happen with the new routing logic
                logger.error(f"Unknown routing strategy: {routing_decision.strategy}")
                # Force route to content calendar for any unhandled case
                routing_decision.strategy = "single_agent"
                routing_decision.target_agents = ["content_calendar_agent"]
                return await self._delegate_to_single_agent(message, routing_decision, context)

        except Exception as e:
            logger.error(f"Error in orchestrator agent: {e}")
            return "I apologize, but I encountered an error while processing your request. Please try again or rephrase your question."

    async def _initialize_components(self, context: Dict[str, Any] = None):
        """Initialize agent registry and microservice tools."""
        try:
            from app.services.orchestrator_service import OrchestratorService

            # Get organization context
            organization_id = context.get("organization_id") if context else None
            db = context.get("db") if context else None

            # Initialize orchestrator service
            orchestrator_service = OrchestratorService()
            self.agent_registry = await orchestrator_service.get_agent_registry(db, organization_id)
            self.microservice_tools = await orchestrator_service.get_microservice_tools(organization_id)

        except Exception as e:
            logger.error(f"Error initializing orchestrator components: {e}")
            # Continue with limited functionality
            self.agent_registry = {}
            self.microservice_tools = {}



    async def _handle_direct_response(self, message: str, context: Dict[str, Any] = None) -> str:
        """Handle requests that don't require delegation to other agents."""
        try:
            # Use the base LangGraph functionality for direct responses
            response = await self.langchain_service.run_agent_workflow(
                message=message,
                instructions=self.instructions,
                conversation_history=context.get("conversation_history", []) if context else [],
                capabilities=[cap.value for cap in self.capabilities]
            )
            return response
        except Exception as e:
            logger.error(f"Error in direct response: {e}")
            return "I can help you with various tasks by coordinating with specialized agents. Could you please provide more details about what you need assistance with?"

    async def _delegate_to_single_agent(self, message: str, routing_decision, context: Dict[str, Any] = None) -> str:
        """Delegate task to a single specialized agent."""
        try:
            target_agents = routing_decision.target_agents
            if not target_agents:
                return await self._handle_direct_response(message, context)

            agent_type = target_agents[0]

            # Get the agent instance
            if self.agent_registry and agent_type in self.agent_registry:
                agent_info = self.agent_registry[agent_type]
                # Extract the actual agent instance
                agent = agent_info.get("instance") if isinstance(agent_info, dict) else agent_info
            else:
                # Fallback to creating agent directly
                agent = await self._create_agent_instance(agent_type, context)

            if not agent or not hasattr(agent, 'process_message'):
                return f"I apologize, but the {agent_type} agent is not properly configured. Let me try to help you directly with your request."

            # Delegate to the agent and return response seamlessly
            response = await agent.process_message(message, context)
            return response

        except Exception as e:
            logger.error(f"Error delegating to single agent: {e}")
            return await self._handle_direct_response(message, context)

    async def _coordinate_multi_agent_workflow(self, message: str, routing_decision, context: Dict[str, Any] = None) -> str:
        """Coordinate a workflow involving multiple agents."""
        try:
            target_agents = routing_decision.target_agents

            if not target_agents:
                return await self._handle_direct_response(message, context)

            responses = []
            current_context = context.copy() if context else {}

            # Execute workflow steps
            for i, agent_type in enumerate(target_agents):
                try:
                    # Get agent instance
                    if self.agent_registry and agent_type in self.agent_registry:
                        agent_info = self.agent_registry[agent_type]
                        # Extract the actual agent instance
                        agent = agent_info.get("instance") if isinstance(agent_info, dict) else agent_info
                    else:
                        agent = await self._create_agent_instance(agent_type, current_context)

                    if not agent or not hasattr(agent, 'process_message'):
                        continue

                    # Prepare message for this agent (may include previous responses)
                    if i == 0:
                        agent_message = message
                    else:
                        # Include previous responses as context
                        previous_responses = "\n\n".join([f"Previous response from {resp['agent']}: {resp['content']}" for resp in responses])
                        agent_message = f"Original request: {message}\n\n{previous_responses}\n\nPlease provide your specialized input on this request."

                    # Get response from agent
                    response = await agent.process_message(agent_message, current_context)

                    # Store response
                    responses.append({
                        "agent": agent.name,
                        "agent_type": agent_type,
                        "content": response
                    })

                    # Update context for next agent
                    current_context["previous_responses"] = responses

                except Exception as e:
                    logger.error(f"Error with agent {agent_type}: {e}")
                    continue

            # Aggregate responses
            if not responses:
                return await self._handle_direct_response(message, context)

            return await self._aggregate_responses(message, responses, routing_decision)

        except Exception as e:
            logger.error(f"Error in multi-agent workflow: {e}")
            return await self._handle_direct_response(message, context)



    async def _aggregate_responses(self, original_message: str, responses: List[Dict[str, Any]], routing_decision=None) -> str:
        """Aggregate multiple agent responses into a unified, seamless response."""
        try:
            if len(responses) == 1:
                # Single response - return directly without any orchestrator metadata
                return responses[0]['content']

            # Create aggregation prompt for multiple responses
            responses_text = "\n\n".join([
                f"Expert perspective {i+1}:\n{resp['content']}"
                for i, resp in enumerate(responses)
            ])

            aggregation_prompt = f"""
            User request: "{original_message}"

            I have gathered the following expert perspectives:

            {responses_text}

            Please provide a unified, comprehensive response that:
            1. Synthesizes the key insights from all perspectives
            2. Addresses the original request completely
            3. Provides clear, actionable guidance
            4. Flows naturally as a single, coherent response
            5. Does not mention multiple sources or coordination

            Present this as if you are a single AI assistant with deep expertise across all relevant domains.
            Focus entirely on helping the user achieve their goals.
            """

            # Use LLM to create seamless unified response
            unified_response = await self.langchain_service.process_message(
                message=aggregation_prompt,
                instructions="You are an expert AI assistant. Synthesize the information into a single, helpful response without revealing multiple sources.",
                conversation_history=[],
                capabilities=["synthesis", "communication"]
            )

            return unified_response

        except Exception as e:
            logger.error(f"Error aggregating responses: {e}")
            # Fallback - still keep it seamless
            if len(responses) == 1:
                return responses[0]['content']

            # Simple synthesis fallback
            result = "Based on comprehensive analysis, here's what I recommend:\n\n"
            for i, resp in enumerate(responses, 1):
                # Extract key points without revealing sources
                content = resp['content']
                result += f"{content}\n\n"

            return result.strip()

    async def _create_agent_instance(self, agent_type: str, context: Dict[str, Any] = None):
        """Create an agent instance for delegation."""
        try:
            # Import agent classes
            agent_classes = {
                "code_generator": CodeGeneratorAgent,
                "accessibility_advisor": AccessibilityAdvisorAgent,
                "documentation_specialist": DocumentationSpecialistAgent,
                "deep_research_agent": DeepResearchAgent,
                "content_calendar_agent": ContentCalendarAgent
            }

            agent_class = agent_classes.get(agent_type)
            if agent_class:
                return agent_class()
            else:
                logger.warning(f"Unknown agent type: {agent_type}")
                return None

        except Exception as e:
            logger.error(f"Error creating agent instance {agent_type}: {e}")
            return None


# Registry of sample agents
SAMPLE_AGENTS = {
    "code_generator": CodeGeneratorAgent,
    "accessibility_advisor": AccessibilityAdvisorAgent,
    "documentation_specialist": DocumentationSpecialistAgent,
    "deep_research_agent": DeepResearchAgent,
    "content_calendar_agent": ContentCalendarAgent,
    "orchestrator_agent": OrchestratorAgent,
}


def get_sample_agent(agent_type: str):
    """Get a sample agent instance by type."""
    agent_class = SAMPLE_AGENTS.get(agent_type)
    if agent_class:
        return agent_class()
    raise ValueError(f"Unknown sample agent type: {agent_type}")


def list_sample_agents() -> Dict[str, Dict[str, Any]]:
    """List all available sample agents with their metadata."""
    agents_info = {}
    for agent_type, agent_class in SAMPLE_AGENTS.items():
        agent = agent_class()
        agents_info[agent_type] = {
            "name": agent.name,
            "description": agent.description,
            "capabilities": [cap.value for cap in agent.capabilities]
        }
    return agents_info
