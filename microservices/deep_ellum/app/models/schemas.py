from pydantic import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class AgentCapability(str, Enum):
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    ACCESSIBILITY_AUDIT = "accessibility_audit"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    DEBUGGING = "debugging"
    WEB_SEARCH = "web_search"
    FILE_ANALYSIS = "file_analysis"

    # Research and Intelligence Capabilities
    COMPETITOR_MONITORING = "competitor_monitoring"
    TREND_ANALYSIS = "trend_analysis"
    REPORT_GENERATION = "report_generation"
    ALERTING = "alerting"
    DATA_AGGREGATION = "data_aggregation"
    MARKET_RESEARCH = "market_research"

    # Content and Planning Capabilities
    CONTENT_CREATION = "content_creation"
    CONTENT_PLANNING = "content_planning"
    CONTENT_STRATEGY = "content_strategy"
    PLANNING = "planning"
    CONVERSATION = "conversation"

    # Orchestrator Capabilities
    ORCHESTRATION = "orchestration"
    TASK_DELEGATION = "task_delegation"
    MULTI_AGENT_COORDINATION = "multi_agent_coordination"
    MICROSERVICE_INTEGRATION = "microservice_integration"
    TASK_COORDINATION = "task_coordination"
    MULTI_AGENT_WORKFLOW = "multi_agent_workflow"
    RESEARCH = "research"


# Base schemas
class CustomAgentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    personality: Optional[str] = None
    instructions: str = Field(..., min_length=1)
    capabilities: Optional[List[AgentCapability]] = []
    is_active: bool = True


class CustomAgentCreate(CustomAgentBase):
    pass


class CustomAgentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1)
    personality: Optional[str] = None
    instructions: Optional[str] = Field(None, min_length=1)
    capabilities: Optional[List[AgentCapability]] = None
    is_active: Optional[bool] = None


class CustomAgentResponse(CustomAgentBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    is_sample: bool
    created_by: str
    organization_id: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]


# Conversation schemas
class ConversationMessageBase(BaseModel):
    role: MessageRole
    content: str
    message_metadata: Optional[Dict[str, Any]] = None


class ConversationMessageCreate(ConversationMessageBase):
    pass


class ConversationMessageResponse(ConversationMessageBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    conversation_id: UUID
    created_at: datetime


class AgentConversationBase(BaseModel):
    title: Optional[str] = None
    is_active: bool = True


class AgentConversationCreate(AgentConversationBase):
    agent_id: UUID


class AgentConversationListItem(AgentConversationBase):
    """Schema for conversation list items (without messages)."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    agent_id: UUID
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime]


class AgentConversationResponse(AgentConversationBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    agent_id: UUID
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime]
    messages: List[ConversationMessageResponse] = []


# Chat schemas
class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1)
    conversation_id: Optional[UUID] = None
    agent_id: UUID


class ChatResponse(BaseModel):
    message: str
    conversation_id: UUID
    agent_name: str
    response_metadata: Optional[Dict[str, Any]] = None


# Agent Template schemas
class AgentTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    category: str = Field(..., min_length=1, max_length=100)
    template_instructions: str = Field(..., min_length=1)
    default_capabilities: Optional[List[AgentCapability]] = []


class AgentTemplateResponse(AgentTemplateBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]


# List responses
class AgentListResponse(BaseModel):
    agents: List[CustomAgentResponse]
    total: int
    page: int
    size: int


# Research Project schemas
class ResearchProjectBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    project_type: str = Field(..., min_length=1, max_length=100)
    configuration: Optional[Dict[str, Any]] = None
    is_active: bool = True


class ResearchProjectCreate(ResearchProjectBase):
    pass


class ResearchProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    project_type: Optional[str] = Field(None, min_length=1, max_length=100)
    configuration: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class ResearchProjectResponse(ResearchProjectBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    organization_id: str
    created_by: str
    created_at: datetime
    updated_at: Optional[datetime]


# Competitor Profile schemas
class CompetitorProfileBase(BaseModel):
    competitor_name: str = Field(..., min_length=1, max_length=255)
    industry: Optional[str] = Field(None, max_length=100)
    website: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    keywords: Optional[List[str]] = []
    monitoring_frequency: str = Field(default="daily", pattern="^(hourly|daily|weekly)$")
    profile_data: Optional[Dict[str, Any]] = None
    is_active: bool = True


class CompetitorProfileCreate(CompetitorProfileBase):
    project_id: UUID


class CompetitorProfileUpdate(BaseModel):
    competitor_name: Optional[str] = Field(None, min_length=1, max_length=255)
    industry: Optional[str] = Field(None, max_length=100)
    website: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    keywords: Optional[List[str]] = None
    monitoring_frequency: Optional[str] = Field(None, pattern="^(hourly|daily|weekly)$")
    profile_data: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class CompetitorProfileResponse(CompetitorProfileBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    project_id: UUID
    organization_id: str
    last_monitored: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]


# Research Finding schemas
class ResearchFindingBase(BaseModel):
    finding_type: str = Field(..., min_length=1, max_length=100)
    title: str = Field(..., min_length=1, max_length=500)
    content: str = Field(..., min_length=1)
    source_url: Optional[str] = Field(None, max_length=1000)
    source_type: Optional[str] = Field(None, max_length=100)
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    keywords: Optional[List[str]] = []
    finding_metadata: Optional[Dict[str, Any]] = None
    is_significant: bool = False


class ResearchFindingCreate(ResearchFindingBase):
    project_id: UUID
    competitor_id: Optional[UUID] = None


class ResearchFindingUpdate(BaseModel):
    finding_type: Optional[str] = Field(None, min_length=1, max_length=100)
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    content: Optional[str] = Field(None, min_length=1)
    source_url: Optional[str] = Field(None, max_length=1000)
    source_type: Optional[str] = Field(None, max_length=100)
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    keywords: Optional[List[str]] = None
    finding_metadata: Optional[Dict[str, Any]] = None
    is_significant: Optional[bool] = None


class ResearchFindingResponse(ResearchFindingBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    project_id: UUID
    competitor_id: Optional[UUID]
    organization_id: str
    created_at: datetime


# Trend Analysis schemas
class TrendAnalysisBase(BaseModel):
    topic: str = Field(..., min_length=1, max_length=255)
    time_period: str = Field(..., min_length=1, max_length=50)
    region: Optional[str] = Field(None, max_length=100)
    analysis_type: str = Field(..., min_length=1, max_length=100)
    trend_data: Dict[str, Any] = Field(..., min_length=1)
    key_insights: Optional[List[str]] = []
    growth_indicators: Optional[List[str]] = []
    emerging_themes: Optional[List[str]] = []
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    data_sources_count: int = Field(default=0, ge=0)


class TrendAnalysisCreate(TrendAnalysisBase):
    pass


class TrendAnalysisResponse(TrendAnalysisBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    organization_id: str
    created_by: str
    created_at: datetime


# Research Report schemas
class ResearchReportBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=500)
    report_type: str = Field(..., min_length=1, max_length=100)
    executive_summary: Optional[str] = None
    content: str = Field(..., min_length=1)
    findings_summary: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = []
    data_sources: Optional[List[str]] = []
    report_metadata: Optional[Dict[str, Any]] = None
    is_published: bool = False


class ResearchReportCreate(ResearchReportBase):
    pass


class ResearchReportUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    report_type: Optional[str] = Field(None, min_length=1, max_length=100)
    executive_summary: Optional[str] = None
    content: Optional[str] = Field(None, min_length=1)
    findings_summary: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = None
    data_sources: Optional[List[str]] = None
    report_metadata: Optional[Dict[str, Any]] = None
    is_published: Optional[bool] = None


class ResearchReportResponse(ResearchReportBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    organization_id: str
    generated_by: str
    created_at: datetime
    updated_at: Optional[datetime]


# Research Alert schemas
class ResearchAlertBase(BaseModel):
    alert_type: str = Field(..., min_length=1, max_length=100)
    title: str = Field(..., min_length=1, max_length=500)
    message: str = Field(..., min_length=1)
    severity: str = Field(default="medium", pattern="^(low|medium|high|critical)$")
    trigger_data: Optional[Dict[str, Any]] = None
    related_finding_id: Optional[UUID] = None
    related_competitor_id: Optional[UUID] = None
    is_read: bool = False
    is_dismissed: bool = False


class ResearchAlertCreate(ResearchAlertBase):
    pass


class ResearchAlertUpdate(BaseModel):
    is_read: Optional[bool] = None
    is_dismissed: Optional[bool] = None


class ResearchAlertResponse(ResearchAlertBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    organization_id: str
    created_at: datetime


class ConversationListResponse(BaseModel):
    conversations: List[AgentConversationListItem]
    total: int
    page: int
    size: int


# Organization Settings schemas
class KnowledgebaseToggleRequest(BaseModel):
    enabled: bool


class SocialsDatabaseToggleRequest(BaseModel):
    enabled: bool


class OrganizationSettingsResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    organization_id: str
    knowledgebase_enabled: bool
    socials_database_enabled: bool
    created_at: datetime
    updated_at: Optional[datetime]
