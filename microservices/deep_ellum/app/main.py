from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.database.database import create_tables
from app.routes import agents, chat, research
from app.utils.logger import get_logger
from app.agents.sample_agents import SAMPLE_AGENTS
from app.models.models import CustomAgent
from app.database.database import AsyncSessionLocal

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Deep Ellum Agent Service...")
    
    try:
        # Create database tables
        await create_tables()
        logger.info("Database tables created successfully")

        # Initialize sample agents in database
        await initialize_sample_agents()
        logger.info("Sample agents initialized")

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        logger.warning("Service will start without database functionality")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Deep Ellum Agent Service...")


async def initialize_sample_agents():
    """Initialize sample agents in the database."""
    try:
        async with AsyncSessionLocal() as db:
            from sqlalchemy import select, and_

            # Get existing sample agents
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.is_sample == True)
            )
            existing_samples = result.scalars().all()
            existing_names = {agent.name for agent in existing_samples}

            agents_created = 0

            # Create sample agents that don't exist yet
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                try:
                    agent_instance = agent_class()

                    # Check if this specific agent already exists
                    if agent_instance.name in existing_names:
                        logger.info(f"Sample agent '{agent_instance.name}' already exists, skipping")
                        continue

                    sample_agent = CustomAgent(
                        name=agent_instance.name,
                        description=agent_instance.description,
                        personality=agent_instance.personality,
                        instructions=agent_instance.instructions,
                        capabilities=[cap.value for cap in agent_instance.capabilities],
                        is_active=True,
                        is_sample=True,
                        created_by="system",
                        organization_id=None  # Sample agents are global, not organization-specific
                    )

                    db.add(sample_agent)
                    agents_created += 1
                    logger.info(f"Created sample agent: {agent_instance.name}")

                except Exception as e:
                    logger.error(f"Error creating sample agent {agent_type}: {e}")

            if agents_created > 0:
                await db.commit()
                logger.info(f"Created {agents_created} new sample agents")
            else:
                logger.info("All sample agents already exist")

    except Exception as e:
        logger.error(f"Error initializing sample agents: {e}")
        # Don't raise the error to allow the service to start without database


# Create FastAPI app
app = FastAPI(
    title="Deep Ellum Custom AI Agent Service",
    description="A service for creating, managing, and chatting with custom AI agents using LangChain and LangGraph",
    version="1.0.0",
    openapi_url="/api/v1/agents/openapi.json",
    docs_url="/api/v1/agents/docs",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(research.router, prefix="/api/v1", tags=["research"])

# Settings router
from app.routes import settings as settings_routes
app.include_router(settings_routes.router, prefix="/api/v1/settings", tags=["settings"])

# AI Provider router
from app.routes import ai_provider
app.include_router(ai_provider.router, prefix="/api/v1", tags=["ai-provider"])

# Deep Research router
try:
    from app.routes import deep_research
    app.include_router(deep_research.router, prefix="/api/v1", tags=["deep-research"])
except ImportError as e:
    logger.warning(f"Deep Research routes not available: {e}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    from app.core.config import settings as config_settings
    return {
        "status": "healthy",
        "service": config_settings.SERVICE_NAME,
        "version": "1.0.0"
    }


@app.get("/agent_status")
async def agent_status():
    """Service status endpoint."""
    from app.core.config import settings as config_settings
    return {
        "message": "Welcome to the Deep Ellum Custom AI Agent Service",
        "service": config_settings.SERVICE_NAME,
        "features": [
            "Custom AI agent creation",
            "LangChain and LangGraph integration",
            "Google Gemini AI",
            "Sample agents (Code Generator, Accessibility Advisor, Documentation Specialist, Deep Research Agent, OpenAI Deep Research Agent)",
            "Conversation management",
            "Agent customization and testing",
            "Intelligent research routing with LLM",
            "Competitor analysis and monitoring",
            "Trend analysis and market research",
            "Data aggregation and synthesis",
            "Automated report generation",
            "Research project management",
            "OpenAI Deep Research integration",
            "o3-deep-research and o4-mini-deep-research models",
            "Multi-step research with web search and code interpretation",
            "Research-analyst-level reports with citations"
        ]
    }


def custom_openapi():
    """Custom OpenAPI schema to include bearer token authentication."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Deep Ellum Custom AI Agent Service",
        version="1.0.0",
        description="A comprehensive service for creating and managing custom AI agents",
        routes=app.routes,
    )

    # Add Bearer token authentication scheme
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }

    # Apply Bearer authentication to all endpoints
    for path in openapi_schema["paths"].values():
        for method in path.values():
            if "security" in method:
                method["security"].append({"Bearer": []})
            else:
                method["security"] = [{"Bearer": []}]

    # Add custom schema information
    openapi_schema["info"]["x-logo"] = {
        "url": "https://example.com/logo.png"
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


if __name__ == "__main__":
    from app.core.config import settings as config_settings
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=config_settings.SERVICE_PORT,
        reload=config_settings.DEBUG,
        log_level="info"
    )
