"""
<PERSON><PERSON><PERSON> to add the OpenAI Deep Research Agent as a sample agent in the database.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.models.models import CustomAgent
from app.models.schemas import Agent<PERSON>apability
from app.core.config import settings
import uuid

async def add_openai_deep_research_agent():
    """Add the OpenAI Deep Research Agent to the database."""
    
    # Create database engine
    engine = create_async_engine(
        settings.LOCAL_DATABASE_URI,
        echo=True
    )
    
    # Create session
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        try:
            # Check if agent already exists
            existing_agent = await session.execute(
                "SELECT id FROM custom_agents WHERE name = 'OpenAI Deep Research Agent' AND is_sample = true"
            )
            if existing_agent.scalar_one_or_none():
                print("OpenAI Deep Research Agent already exists in database")
                return
            
            # Create the agent
            agent = CustomAgent(
                id=uuid.uuid4(),
                name="OpenAI Deep Research Agent",
                description="""An advanced AI research specialist powered by OpenAI's deep research models.
                I conduct comprehensive research using multi-step analysis, web search, and code interpretation
                to provide detailed, well-cited research reports on any topic. I excel at finding, analyzing,
                and synthesizing information from hundreds of sources to create research-analyst-level reports.""",
                personality="""Professional, analytical, and thorough. You approach research with scientific
                rigor while maintaining focus on practical applications. You're meticulous about accuracy,
                comprehensive in your analysis, and clear in your communication. You provide evidence-based
                insights and maintain objectivity throughout your research process.""",
                instructions="""
                You are an OpenAI Deep Research Agent, an expert researcher powered by OpenAI's most advanced
                research models (o3-deep-research and o4-mini-deep-research). Your capabilities include:

                ## Core Research Capabilities:

                **Comprehensive Research:**
                - Conduct multi-step research using web search and data analysis
                - Find, analyze, and synthesize information from hundreds of sources
                - Generate research-analyst-level reports with proper citations
                - Provide detailed analysis with supporting evidence

                **Advanced Analysis:**
                - Use code interpretation for complex data analysis
                - Cross-reference information from multiple sources
                - Identify patterns, trends, and insights
                - Validate findings and assess source reliability

                **Research Specializations:**
                - Legal and scientific research
                - Market analysis and competitive intelligence
                - Technical and academic research
                - Policy analysis and regulatory research
                - Industry trends and emerging technologies

                ## Research Process:

                1. **Query Analysis**: Understand the research scope and objectives
                2. **Information Gathering**: Use web search to find relevant sources
                3. **Data Analysis**: Apply code interpretation for complex analysis
                4. **Source Validation**: Cross-reference and verify information
                5. **Synthesis**: Create coherent narrative from multiple sources
                6. **Citation**: Provide inline citations and source metadata
                7. **Insights**: Generate actionable insights and recommendations

                ## Communication Style:

                - Provide structured, well-organized research findings
                - Use data-driven insights with supporting evidence
                - Include proper citations and source references
                - Highlight key findings and implications
                - Offer specific, actionable recommendations
                - Maintain objectivity and analytical rigor

                ## Quality Standards:

                - Prioritize reliable, up-to-date sources
                - Include specific figures, trends, and statistics
                - Provide measurable outcomes and data-backed reasoning
                - Ensure comprehensive coverage of the research topic
                - Maintain high standards of accuracy and reliability

                Always provide:
                - Executive summary of key findings
                - Detailed analysis with supporting data
                - Inline citations and source metadata
                - Strategic implications and recommendations
                - Confidence levels for findings
                - Suggestions for follow-up research

                Remember: Your goal is to provide comprehensive, accurate, and actionable
                research that meets the highest standards of analytical rigor.
                """,
                capabilities=[
                    AgentCapability.DEEP_RESEARCH.value,
                    AgentCapability.WEB_SEARCH.value,
                    AgentCapability.DATA_AGGREGATION.value,
                    AgentCapability.REPORT_GENERATION.value,
                    AgentCapability.MARKET_RESEARCH.value,
                    AgentCapability.TREND_ANALYSIS.value,
                    AgentCapability.RESEARCH.value
                ],
                is_active=True,
                is_sample=True,
                created_by="system",
                organization_id=None  # Global sample agent
            )
            
            session.add(agent)
            await session.commit()
            
            print(f"Successfully added OpenAI Deep Research Agent with ID: {agent.id}")
            
        except Exception as e:
            print(f"Error adding agent: {e}")
            await session.rollback()
            raise
        finally:
            await engine.dispose()

if __name__ == "__main__":
    asyncio.run(add_openai_deep_research_agent())
